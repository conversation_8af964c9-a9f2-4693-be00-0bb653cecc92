import { NextRequest, NextResponse } from "next/server";
import { initAdmin } from "../../../../firebaseAdminConfig";
import { verifyAppCheck, verifyAuth } from "@/lib/authMiddleware";
import { SendMailHandler } from "@/lib/api-gateway-handlers/handlers";
import { NotificationHandlerManager } from "@/lib/api-gateway-handlers/notification-handlers";
import { UsersHandlerManager } from "@/lib/api-gateway-handlers/users-handler";
import { FollowerHandlerManager } from "@/lib/api-gateway-handlers/follow-handlers";
import { OrdersHandlerManager } from "@/lib/api-gateway-handlers/order-handlers";
import { initFirebase } from "../../../../firebaseConfig";
import { getToken } from "firebase/app-check";

// API GATEWAY
// 39 handlers
const activeControllers = new Map<string, AbortController>();

const handlers: Record<
  string,
  {
    method: "GET" | "POST";
    authRequired: boolean;
    appCheckRequired?: boolean;
    handler: (body: any, uid?: string) => Promise<any>;
  }
> = {
  ping: {
    method: "GET",
    authRequired: false,
    appCheckRequired:false,
    handler: async () => {
      const {appCheck} = await initFirebase();
      console.log({appCheck});
      
       if(appCheck){
const { token } = await getToken(appCheck, true);
console.log("App Check token:", token);
    }
      return { success: true, message: "pong" };
    },
  },
  // app_check_test: {
  //   method: "GET",
  //   authRequired: false,
  //   appCheckRequired:true,
  //   handler: async () => {
  //     return { success: true, message: "pass" };
  //   },
  // },

  /**
   * Mail handler's
   */

  sendMail: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await SendMailHandler(body, uid);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  sendResetPasswordMail: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await SendMailHandler(body, uid);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  /**
   * Notification handler's
   */

  CreateNotification: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await NotificationHandlerManager.getInstance()?.CreateNotification(
          body?.payload
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  UserUnreadNotificationCount: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await NotificationHandlerManager.getInstance()?.UserUnreadNotificationCount(
          body?.payload
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetNotificationsByUserId: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await NotificationHandlerManager.getInstance()?.GetNotificationsByUserId(
          body?.payload
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  BulkCreateNotificationsAndUpdateUnreadCounts: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp =
          await NotificationHandlerManager.getInstance()?.BulkCreateNotificationsAndUpdateUnreadCounts(
            body?.payload
          );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  /**
   * Users handler's
   */
  GetAllUsers: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetAllUsers(body?.payload);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  DeleteUserDetails: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.DeleteUserDetails(body?.payload);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetUserById: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUserById(body?.payload?.userId);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetUserByEventId: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUserByEventId(
          body?.payload?.eventId
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetUserByServicesId: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUserByServicesId(
          body?.payload?.eventId
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetUsersByCategory: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUsersByCategory(
          body?.payload?.category
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetUsersByCategoryWithPost: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUsersByCategoryWithPost(
          body?.payload?.categories,
          body?.payload?.filters
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetUserIdByProfileName: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUserIdByProfileName(
          body?.payload?.profile_name
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetProfileNameByUserId: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetProfileNameByUserId(
          body?.payload?.user_id
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  CheckUserStripeId: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.CheckUserStripeId(
          body?.payload?.userId
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetUserStripeId: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUserStripeId(body?.payload?.uid);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  UpdateUser: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        if (!uid) {
          throw new Error("unauthorised");
        }

        const resp = await UsersHandlerManager.getInstance()?.UpdateUser(
          body?.payload?.userId,
          body?.payload?.updatedData
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  ToggleStarredPost: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        if (!uid) {
          throw new Error("unauthorised");
        }

        const resp = await UsersHandlerManager.getInstance()?.ToggleStarredPost(
          body?.payload?.userId,
          body?.payload?.postId,
          body?.payload?.isStarred,
          uid
        );

        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  ToggleBookMarks: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        if (!uid) {
          throw new Error("unauthorised");
        }

        const resp = await UsersHandlerManager.getInstance()?.ToggleBookMarks(
          body?.payload?.userId,
          body?.payload?.postId,
          body?.payload?.isStarred,
          uid
        );

        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  UpdateSocials: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        if (!uid || body?.payload?.user_id !== uid) {
          throw new Error("unauthorised");
        }

        const resp = await UsersHandlerManager.getInstance()?.UpdateSocials(body?.payload);

        return resp;
      } catch (error) {
        console.log({ error });
        throw new Error(error instanceof Error ? error?.message : "something went wrong!");
      }
    },
  },

  CalculateProfileComplete: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.CalculateProfileComplete(
          body?.payload
        );

        return resp;
      } catch (error) {
        console.log({ error });
        throw new Error(error instanceof Error ? error?.message : "something went wrong!");
      }
    },
  },

  GetUniqueUserName: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUniqueUserName(
          body?.payload?.user_name
        );

        return resp;
      } catch (error) {
        console.log({ error });
        throw new Error(error instanceof Error ? error?.message : "something went wrong!");
      }
    },
  },

  GetUsersByIds: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await UsersHandlerManager.getInstance()?.GetUsersByIds(
          body?.payload?.userIds,
          body?.payload?.filters
        );

        return resp;
      } catch (error) {
        console.log({ error });
        throw new Error(error instanceof Error ? error?.message : "something went wrong!");
      }
    },
  },

  // --------- USERS UPDATED ----------------------
  //   toggleStarredPost✅
  //   toggleBookMarks✅
  //   UpdateSocials✅
  //   calculateProfileComplete✅
  //   getUniqueUserName✅
  //   getUsersByIds✅
  //   getAllUsers ✅
  //   sendResetPassword ✅
  //   getUserIdByPostServiceEvent  ✅
  //   GetUserInfo ✅
  //   <deleteUserDetails> and all shole in backend last✅
  //   getFollowersIds  ✅
  //   getUserById // tricky ✅
  //   getUserByEventId✅
  //   getUserByServicesId ✅
  //   getUsersByCategory ✅
  //   getUsersByCategoryWithPost ✅
  //   checkUserNameExists ✅
  //   getUserIdByProfileName ✅
  //   getProfileNameByUserId ✅
  //   checkUserStripeId ✅
  //   GetUserStripeId✅
  //.  updateUser✅

  /**
   *  Followers
   */
  GetFollowersByUserId: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await FollowerHandlerManager.getInstance()?.GetFollowersByUserId(
          body?.payload?.userId
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  GetFollowingsByUserId: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        const resp = await FollowerHandlerManager.getInstance()?.GetFollowingsByUserId(
          body?.payload?.userId
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  FollowByUserId: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await FollowerHandlerManager.getInstance()?.FollowByUserId(body?.payload);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  UnfollowByUserId: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        const resp = await FollowerHandlerManager.getInstance()?.UnfollowByUserId(body?.payload);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  /**
   *
   * orders
   *
   */
  getOrderById: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        await initAdmin();
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.getOrderById(body?.payload?.id);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },
  markEscrowStageReleased: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.markEscrowStageReleased(
          body?.payload?.orderId,
          body?.payload?.stage,
          body?.payload?.amount,
          body?.payload?.transferId
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  updateOrder: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.updateOrder(
          body?.payload?.id,
          body?.payload?.updatedData
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  // hide usr detail
  GetOrderDetailsByUserId: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.GetOrderDetailsByUserId(
          body?.payload
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  CreateOrder_V2: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.CreateOrder_V2(body?.payload);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },
  AddtoOrderState: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.AddtoOrderState(body?.payload);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },
  UpdateUserAccountId: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        if (uid !== body?.payload?.user_id) {
          throw new Error("Unauthorized");
        }
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.UpdateUserAccountId(body?.payload);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },
  UpdateOrderStripeDetails: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        console.log({ uid });

        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.UpdateOrderStripeDetails(
          body?.payload
        );
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },

  UpdateActivityLog: {
    method: "POST",
    authRequired: true,
    handler: async (body, uid) => {
      try {
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.UpdateActivityLog(body?.payload);
        return resp;
      } catch (error) {
        console.log({ error });
        throw new Error(error instanceof Error ? error?.message : "something went wrong");
      }
    },
  },

  // move to user handler later
  GetUserInfo: {
    method: "POST",
    authRequired: false,
    handler: async (body, uid) => {
      try {
        // check uid has this order (either prodileId or userProfileId)
        const resp = await OrdersHandlerManager?.getInstance()?.GetUserInfo(body?.payload?.uid);
        return resp;
      } catch (error) {
        console.log({ error });
      }
    },
  },
};

export async function POST(request: NextRequest) {
  try {
    await initAdmin();

    const body = await request.json();
    console.log({ body });

    const type = body.type;

    if (!type || !handlers[type]) {
      return NextResponse.json({ success: false, error: "Invalid API type" }, { status: 400 });
    }

    const { authRequired, handler, appCheckRequired } = handlers[type];

    // Run auth check if needed
    let uid: string | undefined;

    if (appCheckRequired) {
      const appCheck = await verifyAppCheck(request);
      if ("status" in appCheck) return appCheck; 
    }

    if (authRequired) {
      const auth = await verifyAuth(request);
      if ("status" in auth) return auth; // error response
      uid = auth.uid;
    }

    // Dispatch to handler
    const response = await handler(body, uid);
    // cleanup after success
    // activeControllers.delete(type);

    return NextResponse.json({ success: true, message: response });
  } catch (error) {
    console.error({ error });
    return NextResponse.json(
      {
        success: false,
        error: "Failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
